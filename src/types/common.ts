export enum PoTypeEnum {
  GENERAL = 'GENERAL',
  MARKET = 'MARKET',
  TOPIC = 'TOPIC',
  SWOT = 'SWOT',
  COMPANY = 'COMPANY',
  REGULATION = 'REGULATION',
  RISK = 'RISK',
}

export enum ProcessStatusEnum {
  STARTING = 'STARTING',
  FINISH_USERINTENT = 'FINISH_USERINTENT',
  FINISH_SEARCH = 'FINISH_SEARCH',
  FINISH_WEBCRAWLER = 'FINISH_WEBCRAWLER',
  FINISH = 'FINISH',
  FAILED = 'FAILED',
  CANCELED = 'CANCELED',
}

export enum ImageStatusEnum {
  FINISH = 'FINISH',
  IN_PROGRESS = 'IN_PROGRESS',
}

export enum PdfStatusEnum {
  FINISH = 'FINISH',
}

export enum ModelEnum {
  CLAUDE = 'CLAUDE',
}

export enum SubscriptionEnum {
  BASIC_MONTH = 'BASIC_MONTH',
  BASIC_YEAR = 'BASIC_YEAR',
  PRO_MONTH = 'PRO_MONTH',
  PRO_YEAR = 'PRO_YEAR',
  FREE = 'FREE',
}

export enum SearchModeEnum {
  AUTO = 'AUTO',
  REASONING = 'REASONING',
  DEEPSEARCH = 'DEEPSEARCH',
}

export const enum ShareTypeEnum {
  GENERAL = 'GENERAL',
  MARKET = 'MARKET',
  COMPANY = 'COMPANY',
  TOPIC = 'TOPIC',
  REGULATION = 'REGULATION',
  SWOT = 'SWOT',
  RISK = 'RISK',
  REPORT = 'REPORT',
  PDF = 'PDF',
}
