import { getCookie } from '@/lib/cookie'
import { useEffect, useRef, useState } from 'react'
import { GeneralSocketMessage, SocketMessages } from './utils'
import { isJsonStr } from '@/lib/utils'

type UseWebSocketWithReconnectionProps = {
  onMessage?: (message: SocketMessages & GeneralSocketMessage) => void // 可选的消息回调函数
  onRetryRefresh?: () => void // 可选的消息回调函数
}

const useWebSocketWithReconnection = ({
  onMessage,
  onRetryRefresh,
}: UseWebSocketWithReconnectionProps) => {
  const socketRef = useRef<WebSocket | null>(null)
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const reconnectAttempts = useRef(0)
  const lastPongTimestamp = useRef(Date.now())
  const inactivityTimeout = 30 * 60 * 1000 // 30 分钟
  const isManualClose = useRef(false) // 新增标记变量
  const countPongRef = useRef<number[]>([])
  let inactivityTimer: ReturnType<typeof setTimeout>
  const startCountPong = (message: { type: string }) => {
    console.log('startCountPong 进入', message)
    if (message.type === 'pong') {
      countPongRef.current.push(Date.now())
      if (countPongRef.current.length >= 2) {
        const last = countPongRef.current[countPongRef.current.length - 1]
        const secondLast = countPongRef.current[countPongRef.current.length - 2]
        const interval = last - secondLast
        console.log(interval, '++++')
        if (interval > 35000) {
          onRetryRefresh?.()
        }
      }
    }
  }

  const handleMessage = (event: MessageEvent) => {
    const message = JSON.parse(isJsonStr(event.data) ? event.data : '{}')
    console.log('receive message:', message)

    if (message.type === 'pong') {
      lastPongTimestamp.current = Date.now()
    }

    startCountPong(message)

    if (onMessage) {
      onMessage(message)
    }
  }

  // 非主动关闭后 重连
  const handleClose = (event: CloseEvent) => {
    if (!isManualClose.current) {
      console.log('WebSocket closed!', event.code, event.reason)
    }
    if (isManualClose.current) {
      console.log('WebSocket Manual closed!', event.code, event.reason)
    }
    setIsConnected(false)
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current)
      heartbeatIntervalRef.current = null
    }
    // 判断是否是主动关闭，非主动关闭时才执行重连
    if (!isManualClose.current) {
      attemptReconnect()
    }
  }

  const startWebSocket = () => {
    if (!socketRef.current) {
      const url = `${process.env.NEXT_PUBLIC_SOKCKET}?Authorization=${encodeURIComponent('Bearer ' + getCookie('smartToken'))}`
      socketRef.current = new WebSocket(url)

      isManualClose.current = false

      socketRef.current.onopen = () => {
        console.log('WebSocket connected!')
        setIsConnected(true)
        reconnectAttempts.current = 0 // 重置重连次数
        lastPongTimestamp.current = Date.now()

        // 发送心跳的定时器
        heartbeatIntervalRef.current = setInterval(() => {
          if (socketRef.current?.readyState === WebSocket.OPEN) {
            socketRef.current.send(JSON.stringify({ type: 'ping' }))
            console.log('send heartbeat!', JSON.stringify({ type: 'ping' }))
          }
        }, 30000)

        if (socketRef.current) {
          // 将消息和关闭事件绑定到 WebSocket 上
          socketRef.current.onmessage = handleMessage
          socketRef.current.onclose = handleClose
        }
      }
    } else {
      if (
        socketRef.current?.readyState === WebSocket.CLOSED ||
        socketRef.current?.readyState === WebSocket.CLOSING
      ) {
        setIsConnected(false)
        socketRef.current = null
        attemptReconnect()
      }
    }
  }

  // 重连
  const attemptReconnect = () => {
    const timeSinceLastPong = Date.now() - lastPongTimestamp.current
    if (reconnectAttempts.current < 4 && timeSinceLastPong >= 10000) {
      // 超过60秒未收到 pong
      reconnectAttempts.current += 1
      console.log(`retry connect ${reconnectAttempts.current}`)
      startWebSocket()
    } else if (reconnectAttempts.current > 3) {
      onRetryRefresh?.()
      console.log('Max reconnection attempts reached. Stopping further attempts.')
    }
  }

  // 监听用户活动，释放连接
  const handleUserActivity = () => {
    clearTimeout(inactivityTimer)
    inactivityTimer = setTimeout(() => {
      console.log('Disconnecting due to 30 minutes of inactivity.')
      closeWebSocket()
    }, inactivityTimeout)
  }

  // 主动关闭
  const closeWebSocket = () => {
    if (socketRef.current) {
      isManualClose.current = true // 标记为主动关闭
      socketRef.current.close()
      socketRef.current = null
      setIsConnected(false)
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current)
        heartbeatIntervalRef.current = null
      }
    }
  }
  // 初始化 - 建立连接,
  useEffect(() => {
    startWebSocket()

    // 监听用户活动以防止自动断开
    window.addEventListener('mousemove', handleUserActivity)
    window.addEventListener('keypress', handleUserActivity)

    // 清理资源
    return () => {
      window.removeEventListener('mousemove', handleUserActivity)
      window.removeEventListener('keypress', handleUserActivity)
      clearTimeout(inactivityTimer)
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current)
        heartbeatIntervalRef.current = null
      }

      if (socketRef.current) {
        isManualClose.current = true
        socketRef.current.close() // 关闭 WebSocket 连接
        socketRef.current = null
      }
    }
  }, [])

  return {
    socket: socketRef.current,
    isConnected,
    closeWebSocket,
    startWebSocket,
    retryFailed: reconnectAttempts.current > 3,
    startCountPong,
  }
}

export default useWebSocketWithReconnection
