import { Modal } from '@/components/business/modal'
import { But<PERSON> } from '@/components/ui/button'
import { Text, TextEnum } from '@/components/business/text'
import { XMarkIcon, CheckIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { useEffect, useState } from 'react'
import { useDeepResearchStore } from '@/store/deep-research'
import { Textinput } from '@/components/business/text-input'
import { Loader } from 'lucide-react'
import { confirmRequirementApi } from '@/api/deep-research'

export const ModalGoals = ({
  open,
  handleCancel,
  handleConfirm,
}: {
  open: boolean
  handleCancel: () => void
  handleConfirm: () => void
}) => {
  const { t } = useTranslation(Namespace.DEEPRESEARCH)
  const { requirement, taskId, setRequirement } = useDeepResearchStore()
  const [isFetched, setIsFetched] = useState(false)
  const [inputValue, setInputValue] = useState(requirement.问题确认)
  const [time, setTime] = useState(100)
  const onChangeTextInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value)
  }
  useEffect(() => {
    if (open) {
      setInputValue(requirement.问题确认)
      setTime(100)
    } else {
      setIsFetched(false)
    }
  }, [open])
  const onConfirm = () => {
    handleConfirm?.()
    setIsFetched(true)
    const timer = setInterval(() => {
      setTime((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)
    // 直接执行
    setRequirement({ ...requirement, 问题确认: inputValue })
    confirmRequirementApi({
      confirmation: inputValue,
      taskId,
      user_language: requirement.用户语言,
      question: requirement.原始问题,
    })
  }
  return (
    <Modal open={open} className='w-[520px] break-all'>
      <div>
        <Text
          type={TextEnum.H4}
          className='flex items-center justify-between border-b border-border px-6 py-4'>
          <div>
            <p>{t('modal.goalsTitle')}</p>
            <p className='text-[12px] font-normal leading-5 text-secondary-black-3'>
              {t('modal.goalsTitleTip')}
            </p>
          </div>
          <XMarkIcon onClick={handleCancel} className='h-5 w-5 cursor-pointer' />
        </Text>
        <div className='pb-5'>
          <Textinput
            placeholder={t('modal.descriptionPlaceholder')}
            isStopEnterPrevent={false}
            className='border-none px-5 py-2 text-sm leading-6 text-secondary-black-2 ring-offset-transparent focus-visible:ring-0 focus-visible:ring-offset-0'
            value={inputValue}
            onChange={onChangeTextInput}
          />
          <div className='mt-2 flex items-center justify-end border-t border-border px-6 pt-4'>
            <Button variant='secondary' size='sm' className='h-10 px-4' onClick={handleCancel}>
              {t('modal.cancel')}
            </Button>
            <Button
              size='sm'
              className='ml-2 h-10 px-4'
              disabled={!inputValue || !requirement.合规判定 || isFetched}
              onClick={() => {
                onConfirm()
              }}>
              {isFetched ? (
                <Loader className='mr-1 h-4 w-4 animate-spin360' />
              ) : (
                <CheckIcon className='mr-1 size-4'></CheckIcon>
              )}
              {t('modal.confirm')}
              {isFetched && time + 's'}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  )
}
