import React, { memo } from 'react'
import clsx from 'clsx'
import { ChevronDownIcon, ChevronRightIcon } from '@heroicons/react/24/outline'
import { Bars3Icon } from '@heroicons/react/24/solid'
import { DraggableOutlineItemProps } from './types'
import TruncatedText from '@/components/ui/TruncatedText'

/**
 * 可拖拽的大纲项目组件
 * 负责渲染可拖拽的大纲项目，支持嵌套拖拽
 */
const DraggableOutlineItem: React.FC<DraggableOutlineItemProps> = memo(
  ({
    item,
    path,
    level,
    expandedItems,
    dragState,
    onToggleExpand,
    onDragStart,
    onDragOver,
    onDragLeave,
    onDrop,
    onDragEnd,
  }) => {
    const isBeingDragged =
      dragState.draggedPath && JSON.stringify(dragState.draggedPath) === JSON.stringify(path)
    const isDropTarget =
      dragState.dropTargetPath && JSON.stringify(dragState.dropTargetPath) === JSON.stringify(path)
    const isExpanded = expandedItems.has(item.chapterIndex as string)
    const hasChildren = item.children && item.children.length > 0

    return (
      <div className={`draggable-item-container level-${level} relative`}>
        {/* 拖拽区域 - 上方，独立于主要内容区域 */}
        <div
          className={clsx(
            'drop-zone drop-zone-before absolute left-0 z-10 h-4 transition-all duration-200',
            // 添加调试背景色，正常情况下透明
            'hover:bg-blue-100/20',
            isDropTarget &&
              dragState.dropPosition === 'before' &&
              'border-t-2 border-primary bg-primary-hover',
          )}
          style={{
            marginLeft: `${level * 20}px`,
            right: 0,
            top: '-8px',
          }}
          onDragOver={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onDragOver(e, path, 'before')
          }}
          onDragLeave={onDragLeave}
          onDrop={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onDrop(e, path, 'before')
          }}
        />

        {/* 主要内容区域 */}
        <div
          draggable={true}
          className={clsx(
            'outline-item group relative cursor-move border-l-2 border-transparent transition-all duration-200 hover:border-primary-disabled',
            isBeingDragged && 'z-50 rotate-1 opacity-50',
            isDropTarget &&
              dragState.dropPosition === 'inside' &&
              'border-primary bg-primary-hover',
          )}
          style={{ marginLeft: `${level * 20}px` }}
          onDragStart={(e) => onDragStart(e, item, path)}
          onDragOver={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onDragOver(e, path, 'inside')
          }}
          onDragLeave={onDragLeave}
          onDrop={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onDrop(e, path, 'inside')
          }}
          onDragEnd={onDragEnd}>
          {/* 拖拽手柄 */}
          <div className='drag-handle absolute left-1 top-1/2 -translate-y-1/2 transform opacity-0 transition-opacity duration-200 group-hover:opacity-100'>
            <Bars3Icon className='h-3 w-3 text-gray-400 hover:text-blue-500' />
          </div>

          {/* 内容 */}
          <div className='ml-6 flex items-center'>
            {/* 展开/收起按钮 */}
            {hasChildren ? (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  e.preventDefault()
                  onToggleExpand(item.chapterIndex as string)
                }}
                className='expand-button mr-2 flex h-4 w-4 items-center justify-center rounded hover:bg-gray-100'
                draggable={false}>
                {isExpanded ? (
                  <ChevronDownIcon className='h-3 w-3 text-gray-500' />
                ) : (
                  <ChevronRightIcon className='h-3 w-3 text-gray-500' />
                )}
              </button>
            ) : (
              <span className='mr-2 h-4 w-4'></span>
            )}

            {/* 标题 */}
            <TruncatedText
              text={`${item.chapterIndex}. ${item.title}`}
              className='flex-1 text-sm font-normal text-secondary-black-3 transition-colors duration-200'
              maxLines={1}
              delayDuration={200}
              tooltipMaxWidth='350px'
              side='top'
            />
          </div>
        </div>

        {/* 拖拽区域 - 下方，独立于主要内容区域 */}
        <div
          className={clsx(
            'drop-zone drop-zone-after absolute left-0 z-10 h-4 transition-all duration-200',
            // 添加调试背景色，正常情况下透明
            'hover:bg-blue-100/20',
            isDropTarget &&
              dragState.dropPosition === 'after' &&
              'border-b-2 border-primary bg-primary-hover',
          )}
          style={{
            marginLeft: `${level * 20}px`,
            right: 0,
            bottom: '-8px',
          }}
          onDragOver={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onDragOver(e, path, 'after')
          }}
          onDragLeave={onDragLeave}
          onDrop={(e) => {
            e.preventDefault()
            e.stopPropagation()
            onDrop(e, path, 'after')
          }}
        />

        {/* 子项目 - 带展开/收起动画 */}
        {hasChildren && (
          <div
            className={clsx(
              'children-container overflow-hidden transition-all duration-300 ease-in-out',
              isExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0',
            )}
            style={{ perspective: '1000px' }}>
            <div
              className='transition-all duration-300 ease-in-out'
              style={{
                transform: isExpanded ? 'rotateX(0deg) scaleY(1)' : 'rotateX(-90deg) scaleY(0.8)',
                transformOrigin: 'center top',
                opacity: isExpanded ? 1 : 0,
              }}>
              {item.children!.map((child, childIndex) => (
                <DraggableOutlineItem
                  key={child.id}
                  item={child}
                  path={[...path, childIndex]}
                  level={level + 1}
                  expandedItems={expandedItems}
                  dragState={dragState}
                  onToggleExpand={onToggleExpand}
                  onDragStart={onDragStart}
                  onDragOver={onDragOver}
                  onDragLeave={onDragLeave}
                  onDrop={onDrop}
                  onDragEnd={onDragEnd}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    )
  },
)

DraggableOutlineItem.displayName = 'DraggableOutlineItem'

export default DraggableOutlineItem
