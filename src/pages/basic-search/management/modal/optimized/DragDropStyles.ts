/**
 * 拖拽样式定义和注入逻辑
 * 从主组件中提取出来，提高代码组织性
 */

export const dragStyles = `
  .dragging {
    opacity: 0.5 !important;
    transform: rotate(2deg) !important;
    z-index: 1000 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  .drag-over {
    border-left-color: #3b82f6 !important;
    background-color: rgba(59, 130, 246, 0.1) !important;
  }

  .drag-handle {
    cursor: grab;
  }

  .drag-handle:active {
    cursor: grabbing;
  }

  .drop-zone {
    opacity: 0;
    pointer-events: auto;
    transition: opacity 0.2s ease;
  }

  .drop-zone:hover,
  .drop-zone.drag-over,
  .dragging ~ .drop-zone {
    opacity: 1;
  }

  /* 拖拽时显示所有拖拽区域 */
  .draggable-outline-container.dragging .drop-zone {
    opacity: 0.3;
    background-color: rgba(59, 130, 246, 0.05);
  }

  .draggable-outline-container.dragging .drop-zone:hover {
    opacity: 1;
    background-color: rgba(59, 130, 246, 0.1);
  }

  .draggable-item-container {
    position: relative;
  }

  .children-container {
    margin-left: 0px;
    position: relative;
  }

  .level-0 {
    border-left: none;
  }

  /* 确保子级项目默认没有左边框，覆盖全局的border-border样式 */
  .level-1 .outline-item,
  .level-2 .outline-item,
  .level-3 .outline-item {
    margin-left: 8px;
    border-left-color: transparent !important;
  }

  /* 子级项目的左边框只在hover时显示 */
  .level-1 .outline-item:hover,
  .level-2 .outline-item:hover,
  .level-3 .outline-item:hover {
    border-left-color: #c5d4ff !important;
  }

  /* 展开/收起按钮样式 */
  .expand-button {
    transition: transform 0.2s ease;
  }

  .expand-button:hover {
    transform: scale(1.1);
  }

  /* 拖拽时隐藏展开按钮的交互 */
  .dragging .expand-button {
    pointer-events: none;
  }
`

/**
 * 注入拖拽样式到页面
 * 确保样式只注入一次
 */
export const injectDragStyles = (): void => {
  if (typeof document === 'undefined') return

  const existingStyle = document.head.querySelector('style[data-drag-styles]')
  if (existingStyle) return

  const styleElement = document.createElement('style')
  styleElement.textContent = dragStyles
  styleElement.setAttribute('data-drag-styles', 'true')
  document.head.appendChild(styleElement)
}

/**
 * 清理拖拽相关的CSS类
 */
export const cleanupDragClasses = (): void => {
  if (typeof document === 'undefined') return

  document.querySelectorAll('.dragging, .drag-over').forEach((el) => {
    el.classList.remove('dragging', 'drag-over')
  })

  // 清理容器的拖拽状态
  const container = document.querySelector('.draggable-outline-container')
  if (container) {
    container.classList.remove('dragging')
  }
}
