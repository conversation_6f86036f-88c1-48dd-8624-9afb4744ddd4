import React, { useState, useCallback, useEffect, useMemo } from 'react'
import { Modal } from '@/components/business/modal'
import { Button } from '@/components/ui/button'
import { Text, TextEnum } from '@/components/business/text'
import { ChevronRightIcon, EyeSlashIcon, EyeIcon, ArrowLeftIcon } from '@heroicons/react/24/outline'
import { XMarkIcon, CheckIcon } from '@heroicons/react/24/solid'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { useDeepResearchStore } from '@/store/deep-research'
import { Loader } from 'lucide-react'
import clsx from 'clsx'
import { useRouter } from 'next/router'

// 导入优化后的组件和hooks
import { ModalOutlineProps } from './types'
import { injectDragStyles } from './DragDropStyles'
import { useDragDrop } from './useDragDrop'
import { useOutlineManagement } from './useOutlineManagement'
import OutlineEditor from './OutlineEditor'
import OutlinePreview from './OutlinePreview'
import { smartiesRoutes } from '@/routers'
import { PoTypeEnum } from '@/types'

/**
 * 优化后的大纲确认模态框组件
 *
 * 主要优化：
 * 1. 组件拆分 - 将大型组件拆分为多个小组件
 * 2. 自定义Hook - 提取拖拽和大纲管理逻辑
 * 3. 性能优化 - 使用memo、useCallback、useMemo
 * 4. 类型安全 - 完整的TypeScript接口
 * 5. 代码组织 - 更好的文件结构和关注点分离
 *
 * 保持100%兼容原有功能和样式
 */
export const ModalOutlineOptimized: React.FC<ModalOutlineProps> = ({
  open,
  handleCancel,
  handleToGols,
}) => {
  const { t } = useTranslation(Namespace.DEEPRESEARCH)
  const { outline, setOutline, startTask, requirement, taskId } = useDeepResearchStore()
  const router = useRouter()
  // 本地状态
  const [isFetched, setIsFetched] = useState(false)

  // 注入拖拽样式
  useEffect(() => {
    injectDragStyles()
  }, [])

  // 重置状态当模态框关闭时
  useEffect(() => {
    if (open) {
      // setInputValue(requirement.问题确认)
    } else {
      setIsFetched(false)
    }
  }, [open, outline.问题确认])

  // 大纲更新回调
  const handleOutlineUpdate = useCallback(
    (newOutline: typeof outline.报告大纲) => {
      const updatedOutline = {
        ...outline,
        报告大纲: newOutline,
      }
      setOutline(updatedOutline)
    },
    [outline, setOutline],
  )

  // 大纲管理Hook
  const {
    expandedItems,
    showDescriptions,
    toggleExpand,
    toggleExpandAll,
    toggleShowDescriptions,
    updateOutlineItem,
    deleteChapter,
    addChapter,
    addSubChapter,
  } = useOutlineManagement(outline.报告大纲, handleOutlineUpdate, open)

  // 拖拽Hook
  const { dragState, handleDragStart, handleDragOver, handleDragLeave, handleDrop, handleDragEnd } =
    useDragDrop(outline.报告大纲, handleOutlineUpdate)

  // 确认按钮处理
  const onConfirm = async () => {
    // 这里保持原有的确认逻辑
    setIsFetched(true)
    // 可以在这里添加API调用逻辑
    await startTask({
      messages: [
        {
          role: 'user',
          content: `原始问题：${outline.原始问题}和用户确认的内容：${outline.问题确认}`,
        },
      ],
      outline: outline.报告大纲,
      question: outline.原始问题,
      requirement: outline.问题确认,
      task_id: taskId,
      user_language: requirement.用户语言,
    })
    router.push({
      pathname: smartiesRoutes.basicSearch.chat(PoTypeEnum.GENERAL.toLocaleLowerCase()),
      query: {
        taskId,
        from: 'deep-research',
      },
    })
    handleCancel()
    setIsFetched(false)
  }

  // 计算展开状态文本
  const expandAllText = useMemo(() => {
    return expandedItems.size === 0 ? t('outline.expandAll') : t('outline.collapseAll')
  }, [expandedItems.size, t])

  // 计算描述显示文本
  const descriptionText = useMemo(() => {
    return showDescriptions ? t('outline.hideAllDescriptions') : t('outline.showAllDescriptions')
  }, [showDescriptions, t])

  // 确认按钮是否禁用
  const isConfirmDisabled = useMemo(() => {
    return !outline.合规判定 || isFetched
  }, [outline.合规判定, isFetched])

  return (
    <Modal open={open} className='w-[1200px] break-all'>
      <div>
        {/* 头部 */}
        <Text
          type={TextEnum.H4}
          className='flex items-center justify-between border-b border-border px-6 py-4'>
          <div>
            <p>{t('outline.title')}</p>
            <p className='mt-1 text-[12px] font-normal leading-5 text-secondary-black-3'>
              {t('outline.subtitle')}
            </p>
          </div>
          <div className='flex items-center gap-4'>
            {/* 展开/收起全部按钮 */}
            <a
              onClick={toggleExpandAll}
              className='flex cursor-pointer select-none items-center text-sm font-normal'>
              <ChevronRightIcon
                className={clsx(
                  'mr-1 size-4 transition-transform duration-300 ease-in-out',
                  expandedItems.size === 0 ? 'rotate-0' : 'rotate-90',
                )}
              />
              {expandAllText}
            </a>

            {/* 显示/隐藏描述按钮 */}
            <a
              onClick={toggleShowDescriptions}
              className='flex cursor-pointer select-none items-center text-sm font-normal'>
              {showDescriptions ? (
                <>
                  <EyeSlashIcon className='mr-1 size-4' />
                  {descriptionText}
                </>
              ) : (
                <>
                  <EyeIcon className='mr-1 size-4' />
                  {descriptionText}
                </>
              )}
            </a>

            {/* 关闭按钮 */}
            <XMarkIcon onClick={handleCancel} className='h-5 w-5 cursor-pointer' />
          </div>
        </Text>

        {/* 主体内容 */}
        <div className='pb-4'>
          <div className='flex h-[calc(100vh-200px)] px-2 pl-3'>
            {/* 左侧编辑区域 */}
            <OutlineEditor
              outline={outline.报告大纲}
              showDescriptions={showDescriptions}
              expandedItems={expandedItems}
              onToggleExpand={toggleExpand}
              onUpdateItem={updateOutlineItem}
              onDeleteChapter={deleteChapter}
              onAddChapter={addChapter}
              onAddSubChapter={addSubChapter}
            />

            {/* 右侧预览区域 */}
            <OutlinePreview
              outline={outline.报告大纲}
              expandedItems={expandedItems}
              dragState={dragState}
              onToggleExpand={toggleExpand}
              onDragStart={handleDragStart}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onDragEnd={handleDragEnd}
            />
          </div>

          {/* 底部按钮 */}
          <div className='flex items-center justify-between border-t border-border px-6 pt-4 text-right'>
            <Button
              variant='secondary'
              size='sm'
              className='border-border px-4 text-secondary-black-3 hover:bg-secondary'
              onClick={handleToGols}>
              <ArrowLeftIcon className='mr-1 size-4'></ArrowLeftIcon>
              {t('modal.backToRequirements')}
            </Button>
            <Button
              size='sm'
              className='ml-2 px-4'
              disabled={isConfirmDisabled}
              onClick={onConfirm}>
              {isFetched ? (
                <Loader className='mr-1 h-4 w-4 animate-spin360' />
              ) : (
                <CheckIcon className='mr-1 size-4' />
              )}
              {t('modal.confirmTitle')}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  )
}
