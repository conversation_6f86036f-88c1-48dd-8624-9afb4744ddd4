import React from 'react'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'

// 定义大纲项的类型
interface OutlineItem {
  id: string
  title: string
  description: string
  chapterIndex: string
  level: string
  children: OutlineItem[]
}

// 定义研究消息类型
interface ResearchMessage {
  ROLE: string
  TYPE: string
  STEP: string
  CONTENT: {
    ACTION?: string
    CHAPTER?: string
    MESSAGE?: string
  }
}

// 定义新的章节内容项类型，可以是普通消息或Markdown内容
interface ChapterContentItem {
  type: 'message' | 'markdown'
  message?: ResearchMessage
  content?: string
  isStreaming?: boolean
}

interface ResearchProcessSectionProps {
  outline: OutlineItem[]
  researchMessages: ResearchMessage[]
  chapterContentItems: Map<string, ChapterContentItem[]>
  isGlobalLoading: boolean
  researchStatus: string
}

const ACTION_COLOR_MAP = {
  Planning: {
    background: 'rgba(7, 188, 212, 0.3)',
    color: 'rgba(5, 126, 142, 1)',
  },
  Searching: {
    background: 'rgba(255, 152, 0, 0.3)',
    color: 'rgba(142, 70, 4, 1)',
  },
  Analyzing: {
    background: 'rgba(146, 227, 174, 0.3)',
    color: 'rgba(69, 113, 84, 1)',
  },
  Thinking: {
    background: 'rgba(157, 18, 214, 0.2)',
    color: 'rgba(107, 14, 145, 1)',
  },
  REFLECTING: {
    background: 'rgba(180, 180, 180, 0.3)',
    color: 'rgba(99, 99, 99, 1)',
  },
}
const ResearchProcessSection: React.FC<ResearchProcessSectionProps> = ({
  outline,
  researchMessages,
  chapterContentItems,
  isGlobalLoading,
  researchStatus,
}) => {
  const { t } = useTranslation(Namespace.DEEPRESEARCH)

  console.log('🔍 ResearchProcessSection render:', {
    outlineLength: outline?.length || 0,
    messagesLength: researchMessages?.length || 0,
    chaptersCount: chapterContentItems?.size || 0,
    isGlobalLoading,
    researchStatus,
  })
  // 获取所有章节索引（用于初始渲染章节等待区）
  const getAllChapterIndices = (items: OutlineItem[]): string[] => {
    let indices: string[] = []

    items.forEach((item) => {
      if (item.chapterIndex) {
        indices.push(item.chapterIndex)
      }

      if (item.children && item.children.length > 0) {
        indices = [...indices, ...getAllChapterIndices(item.children)]
      }
    })
    return indices
  }

  // 根据章节索引查找章节标题
  const findChapterTitle = (chapterIndex: string, items: OutlineItem[]): string => {
    for (const item of items) {
      if (item.chapterIndex === chapterIndex) {
        return item.title
      }

      if (item.children && item.children.length > 0) {
        const title = findChapterTitle(chapterIndex, item.children)
        if (title) return title
      }
    }

    return ''
  }

  // 渲染消息
  const renderMessage = (message: ResearchMessage, index: number) => {
    // 处理工作流开始或结束消息
    if (message.ROLE === 'WORKFLOW' && (message.STEP === 'START' || message.STEP === 'END')) {
      return (
        <div key={index} className='my-1.5 flex justify-center'>
          <div className='rounded-full bg-gray-100 px-2 py-0.5 text-xs text-gray-500'>
            {message.STEP === 'START'
              ? t('researchResults.startingAnalysis')
              : t('researchResults.completed')}
          </div>
        </div>
      )
    }

    // 处理思考类型消息
    if (message.ROLE === 'RESEARCHER' && message.TYPE === 'THINKING' && message.CONTENT?.MESSAGE) {
      return (
        <div
          key={index}
          className='mb-1.5 flex items-center gap-2 rounded-md bg-background px-3 py-2'>
          <div className='flex items-center gap-1.5'>
            <div
              className='rounded-[4px] px-1.5 py-0.5 text-xs font-normal'
              style={{
                backgroundColor:
                  message?.CONTENT.ACTION &&
                  ACTION_COLOR_MAP[message.CONTENT.ACTION as keyof typeof ACTION_COLOR_MAP]
                    ? ACTION_COLOR_MAP[message.CONTENT.ACTION as keyof typeof ACTION_COLOR_MAP]
                        .background
                    : undefined,
                color:
                  message?.CONTENT.ACTION &&
                  ACTION_COLOR_MAP[message.CONTENT.ACTION as keyof typeof ACTION_COLOR_MAP]
                    ? ACTION_COLOR_MAP[message.CONTENT.ACTION as keyof typeof ACTION_COLOR_MAP]
                        .color
                    : undefined,
              }}>
              {message.CONTENT.ACTION ? message.CONTENT.ACTION : message.ROLE}
            </div>
            <span className='text-xs text-secondary-black-3'>{message.CONTENT.MESSAGE}</span>
          </div>
        </div>
      )
    }

    // 默认消息样式, 只处理 RESEARCHER 的消息
    if (message.ROLE === 'RESEARCHER' && message.CONTENT?.MESSAGE) {
      return (
        <div key={index} className='mb-1.5 flex items-center gap-2 rounded px-2 py-1'>
          <div className='flex items-center gap-1.5 whitespace-nowrap'>
            <div className='rounded-full bg-gray-100 px-1.5 py-0.5 text-xs font-medium text-gray-700'>
              {message.ROLE}
            </div>
            <div className='text-xs text-gray-500'>{message.STEP}</div>
            <span className='text-xs text-gray-700'>{message.CONTENT.MESSAGE}</span>
          </div>
        </div>
      )
    }

    return null
  }

  return (
    <div className='transition-all duration-300 ease-in-out'>
      <div className='max-w-none'>
        <div>
          {researchMessages.length > 0 ? (
            <div className='space-y-4'>
              {getAllChapterIndices(outline).map((chapterIndex) => {
                const chapterTitle = findChapterTitle(chapterIndex, outline)
                const contentItems = chapterContentItems.get(chapterIndex) || []
                // 检查章节是否已完成
                const isChapterCompleted = researchMessages.some(
                  (msg) =>
                    msg.ROLE === 'RESEARCHER' &&
                    msg.TYPE === 'REPORTING' &&
                    msg.STEP === 'END' &&
                    msg.CONTENT?.CHAPTER === chapterIndex,
                )

                // 如果章节没有内容，显示等待提示
                if (contentItems.length === 0) {
                  return (
                    <div
                      key={chapterIndex}
                      id={`process-chapter-${chapterIndex.replace(/\./g, '-')}`}
                      className='rounded-lg border border-gray-200 p-4'>
                      <h4 className='mb-2 flex items-center gap-2 font-medium text-indigo-700'>
                        <span>
                          {chapterIndex} {chapterTitle}
                        </span>
                        {isChapterCompleted && (
                          <div className='flex h-4 w-4 items-center justify-center rounded-full bg-green-500'>
                            <svg
                              className='h-2.5 w-2.5 text-white'
                              fill='currentColor'
                              viewBox='0 0 20 20'>
                              <path
                                fillRule='evenodd'
                                d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                                clipRule='evenodd'
                              />
                            </svg>
                          </div>
                        )}
                      </h4>
                      <div className='flex h-12 items-center justify-center rounded-md bg-gray-50'>
                        <span className='text-sm text-gray-500'>
                          {t('researchResults.waiting')}
                        </span>
                      </div>
                    </div>
                  )
                }

                // 确定是否显示章节loading指示器
                const showLoading =
                  isGlobalLoading &&
                  (researchStatus === 'RESEARCHING' ||
                    researchStatus === 'DRAFTING' ||
                    researchStatus === 'REFLECTING')

                return (
                  <div
                    key={chapterIndex}
                    id={`process-chapter-${chapterIndex.replace(/\./g, '-')}`}
                    className='rounded-md bg-card px-4 py-3'>
                    <h4 className='mb-2 flex items-center gap-2 font-medium'>
                      <span>
                        {chapterIndex} {chapterTitle}
                      </span>
                      {isChapterCompleted && (
                        <div className='flex h-4 w-4 items-center justify-center rounded-full bg-green-500'>
                          <svg
                            className='h-2.5 w-2.5 text-white'
                            fill='currentColor'
                            viewBox='0 0 20 20'>
                            <path
                              fillRule='evenodd'
                              d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                              clipRule='evenodd'
                            />
                          </svg>
                        </div>
                      )}
                    </h4>
                    <div className='space-y-2'>
                      {contentItems.map(
                        (item, index) =>
                          item.type === 'message' &&
                          item.message &&
                          renderMessage(item.message, index),
                      )}

                      {/* 最后显示加载指示器 */}
                      {showLoading && (
                        <div className='mt-4 flex items-center space-x-2 py-2 pl-2 text-gray-400'>
                          <div className='h-2 w-2 animate-pulse rounded-full bg-indigo-500'></div>
                          <div className='h-2 w-2 animate-pulse rounded-full bg-indigo-500 delay-150'></div>
                          <div className='h-2 w-2 animate-pulse rounded-full bg-indigo-500 delay-300'></div>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className='flex min-h-[400px] items-center justify-center'>
              <div className='text-center'>
                <div className='mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-indigo-500'></div>
                <p className='mt-4 text-gray-500'>{t('researchResults.processingWait')}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ResearchProcessSection
