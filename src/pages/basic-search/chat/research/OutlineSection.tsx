import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { ChevronRightIcon, ChevronDownIcon } from '@heroicons/react/24/outline'

// 定义大纲项的类型
interface OutlineItem {
  id: string
  title: string
  description: string
  chapterIndex: string
  level: string
  children: OutlineItem[]
}

// 定义研究消息类型
interface ResearchMessage {
  ROLE: string
  TYPE: string
  STEP: string
  CONTENT: {
    ACTION?: string
    CHAPTER?: string
    MESSAGE?: string
  }
}

interface OutlineSectionProps {
  outline: OutlineItem[]
  researchMessages: ResearchMessage[]
  researchStatus: string
  isFailed: boolean
  onScrollToChapter?: (chapterIndex: string) => void
}

const OutlineSection: React.FC<OutlineSectionProps> = ({
  outline,
  researchMessages,
  researchStatus,
  isFailed,
  onScrollToChapter,
}) => {
  const { t } = useTranslation(Namespace.DEEPRESEARCH)

  // 管理展开/收起状态
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())

  // 递归收集所有项目的 ID
  const getAllItemIds = (items: OutlineItem[]): string[] => {
    let ids: string[] = []
    items.forEach((item) => {
      ids.push(item.id)
      if (item.children && item.children.length > 0) {
        ids = [...ids, ...getAllItemIds(item.children)]
      }
    })
    return ids
  }

  // 当 outline 数据变化时，默认展开所有项目
  useEffect(() => {
    if (outline && outline.length > 0) {
      const allIds = getAllItemIds(outline)
      setExpandedItems(new Set(allIds))
    }
  }, [outline])

  // 添加滚动到指定章节的函数
  const scrollToChapter = (chapterIndex: string) => {
    console.log('📍 Outline: Scrolling to chapter', chapterIndex)
    if (onScrollToChapter) {
      onScrollToChapter(chapterIndex)
    } else {
      console.warn('⚠️ onScrollToChapter function not provided')
    }
  }

  // 切换展开/收起状态
  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId)
    } else {
      newExpanded.add(itemId)
    }
    setExpandedItems(newExpanded)
  }

  // 渲染大纲子项
  const renderOutlineItems = (items: OutlineItem[], indent = 0) => {
    return items.map((item) => {
      const hasChildren = item.children && item.children.length > 0
      const isExpanded = expandedItems.has(item.id)

      // 获取章节的状态
      const chapterStatus = researchMessages.find(
        (msg) =>
          msg.ROLE === 'RESEARCHER' &&
          msg.TYPE === 'REPORTING' &&
          msg.STEP === 'END' &&
          msg.CONTENT?.CHAPTER === item.chapterIndex,
      )
        ? 'END'
        : researchStatus

      // 根据状态设置图标
      let statusIcon = null

      if (chapterStatus === 'END') {
        statusIcon = (
          <div className='flex h-4 w-4 items-center justify-center rounded-full bg-green-500'>
            <svg className='h-2.5 w-2.5 text-white' fill='currentColor' viewBox='0 0 20 20'>
              <path
                fillRule='evenodd'
                d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
                clipRule='evenodd'
              />
            </svg>
          </div>
        )
      } else {
        statusIcon = (
          <div
            className='h-4 w-4 animate-pulse rounded-full'
            style={{ background: 'rgba(249, 217, 1, 0.3)' }}>
            <div className='relative mr-2'>
              <div
                className='opacity-1 absolute left-1 top-1 h-2 w-2 rounded-full'
                style={{
                  background: 'rgba(249, 217, 1, 1)',
                }}></div>
              <div className='absolute left-1 top-1 h-2 w-2 animate-ping rounded-full bg-current opacity-75'></div>
            </div>
          </div>
        )
      }

      return (
        <div key={item.id} className='mb-1'>
          <div className='flex items-center' style={{ marginLeft: `${indent * 16}px` }}>
            {/* 展开/收起按钮 */}
            {hasChildren ? (
              <button
                onClick={() => toggleExpanded(item.id)}
                className='mr-2 flex h-4 w-4 items-center justify-center'>
                {isExpanded ? (
                  <ChevronDownIcon className='h-3 w-3' />
                ) : (
                  <ChevronRightIcon className='h-3 w-3' />
                )}
              </button>
            ) : (
              <div className='mr-2 w-4'></div>
            )}

            {/* 章节标题 */}
            <button
              onClick={() => scrollToChapter(item.chapterIndex)}
              className='group -mx-2 flex flex-1 items-center rounded px-2 py-1 text-left font-normal text-secondary-black-2 transition-colors duration-200 hover:bg-blue-50 hover:text-blue-600'>
              <span className='mr-2 flex items-center gap-1'>
                <span>
                  {item.chapterIndex} {item.title}
                </span>
                <svg
                  className='h-3 w-3 text-gray-400 opacity-0 transition-opacity group-hover:opacity-100'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'>
                  <path
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                    d='M13 7l5 5m0 0l-5 5m5-5H6'
                  />
                </svg>
              </span>
            </button>

            {/* 状态图标 */}
            {!isFailed && <div className='ml-2'>{statusIcon}</div>}
          </div>

          {/* 子项 */}
          {hasChildren && isExpanded && (
            <div className='mt-1'>{renderOutlineItems(item.children, indent + 1)}</div>
          )}
        </div>
      )
    })
  }

  return (
    <div>
      <div className='w-[300px] p-4'>
        <div className='mb-4 flex items-center justify-between'>
          <h2 className='text-lg font-semibold text-secondary-black-3'>{t('outline.outline')}</h2>
        </div>
        <div className='max-h-[50vh] min-h-[500px] overflow-y-auto'>
          {outline && outline.length > 0 ? (
            <div className='space-y-2'>{renderOutlineItems(outline)}</div>
          ) : (
            <p className='text-sm text-gray-500'>{t('display.noData')}</p>
          )}
        </div>
      </div>
    </div>
  )
}

export default OutlineSection
