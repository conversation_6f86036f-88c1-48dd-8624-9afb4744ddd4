/* eslint-disable @next/next/no-img-element */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import MarkdownParser from '@/components/business/markdown-parser'
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useDownloadMarkdown } from '@/lib/hooks/useDownloadMarkdown'
import { taskDownloadApi } from '@/api/deep-research'

// 定义大纲项的类型
interface OutlineItem {
  id: string
  title: string
  description: string
  chapterIndex: string
  level: string
  children: OutlineItem[]
}

// 定义新的章节内容项类型，可以是普通消息或Markdown内容
interface ChapterContentItem {
  type: 'message' | 'markdown'
  message?: any
  content?: string
  isStreaming?: boolean
}

interface ResearchDraftSectionProps {
  outline: OutlineItem[]
  chapterContentItems: Map<string, ChapterContentItem[]>
  isGlobalLoading: boolean
  researchStatus: string
  streamingComplete: boolean
  isFailed: boolean
  onRetryDeepResearch?: () => void
  taskId: string
}

const ResearchDraftSection: React.FC<ResearchDraftSectionProps> = ({
  outline,
  chapterContentItems,
  isGlobalLoading,
  // researchStatus,
  streamingComplete,
  isFailed,
  onRetryDeepResearch,
  taskId,
}) => {
  const { t } = useTranslation(Namespace.DEEPRESEARCH)

  // 获取所有章节索引（用于初始渲染章节等待区）
  const getAllChapterIndices = (items: OutlineItem[]): string[] => {
    let indices: string[] = []

    items.forEach((item) => {
      if (item.chapterIndex) {
        indices.push(item.chapterIndex)
      }

      if (item.children && item.children.length > 0) {
        indices = [...indices, ...getAllChapterIndices(item.children)]
      }
    })

    return indices
  }

  // 根据章节索引查找章节标题
  const findChapterTitle = (chapterIndex: string, items: OutlineItem[]): string => {
    for (const item of items) {
      if (item.chapterIndex === chapterIndex) {
        return item.title
      }

      if (item.children && item.children.length > 0) {
        const title = findChapterTitle(chapterIndex, item.children)
        if (title) return title
      }
    }

    return ''
  }

  // 渲染失败状态
  const renderFailedState = () => (
    <div className='flex min-h-[240px] flex-col items-center justify-center'>
      <div className='mt-12 p-2'>
        <img
          src='/images/report_failed.png'
          className='rounded-[4px]'
          alt='AI webpage report failed image'
        />
      </div>
      <div className='max-w-[240px] text-center'>
        <h2 className='mt-4 font-bold'>{t('researchResults.researchFailedTitle')}</h2>
        <p className='mt-4 text-sm text-secondary-black-3'>{t('researchResults.researchFailed')}</p>
      </div>
      <button
        className='mx-auto mt-6 h-10 w-[240px] cursor-pointer rounded-md border border-transparent bg-primary px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2'
        onClick={onRetryDeepResearch}
        asm-tracking='TEST_CLICK_RETRY_RESEARCH_RESULT_PAGE:CLICK'>
        {t('researchResults.retry')}
      </button>
      <button className='mt-5 text-sm text-primary'>{t('researchResults.refundTitle')}</button>
    </div>
  )

  // 渲染加载状态
  const renderLoadingState = () => (
    <div className='flex min-h-[400px] items-center justify-center'>
      <div className='text-center'>
        <div className='mx-auto h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary'></div>
        <p className='text-secondary-black-3s mt-4'>{t('researchResults.processingWait')}</p>
      </div>
    </div>
  )

  // 渲染章节内容
  const renderChapterContent = () => (
    <div>
      <div className='space-y-4'>
        {getAllChapterIndices(outline).map((chapterIndex) => {
          const contentItems = chapterContentItems.get(chapterIndex) || []
          const chapterTitle = findChapterTitle(chapterIndex, outline)

          // 获取该章节的markdown内容, 只取最后一次更新
          const markdownContent =
            contentItems.filter((item) => item.type === 'markdown').pop()?.content || ''

          return (
            <div
              key={chapterIndex}
              id={`draft-chapter-${chapterIndex.replace(/\./g, '-')}`}
              className='chapter-section mb-6 scroll-mt-4'>
              {/* 章节标题 */}
              <h3 className='mb-4 scroll-mt-4 border-b border-gray-200 pb-2 text-lg font-semibold text-gray-800'>
                {chapterIndex} {chapterTitle}
              </h3>

              {/* 章节内容 */}
              {markdownContent ? (
                <MarkdownParser content={markdownContent} />
              ) : (
                <div className='flex h-20 items-center justify-center rounded-md bg-gray-50'>
                  <span className='text-sm text-gray-500'>{t('researchResults.waiting')}</span>
                </div>
              )}
            </div>
          )
        })}
      </div>

      {isGlobalLoading && (
        <div className='bg-card-50 sticky bottom-0 mt-4 flex items-center justify-center space-x-2 py-3 backdrop-blur-sm'>
          <div className='flex items-center space-x-2'>
            <div className='h-3 w-3 animate-pulse rounded-full bg-primary'></div>
            <div className='h-3 w-3 animate-pulse rounded-full bg-primary delay-150'></div>
            <div className='h-3 w-3 animate-pulse rounded-full bg-primary delay-300'></div>
          </div>
          <span className='ml-2 text-sm font-medium text-primary'>
            {t('researchResults.updating')}
          </span>
        </div>
      )}
    </div>
  )
  // 下载
  const { exportToWord } = useDownloadMarkdown({
    content: getAllChapterIndices(outline).reduce((acc, curr) => {
      const contentItems = chapterContentItems.get(curr) || []
      const markdownContent =
        contentItems.filter((item) => item.type === 'markdown').pop()?.content || ''
      return acc + markdownContent
    }, ''),
  })
  const onDownloadWord = () => {
    exportToWord(`${t('researchResults.researchDrafting')}.docx`)
    handleTaskDownload()
  }
  const handleTaskDownload = async () => {
    try {
      const res = await taskDownloadApi({ task_id: taskId, useReward: true })

      console.log('Task download request successful:', res.data)
      return res.data
    } catch (error) {
      console.error('check download available error:', error)
      return false
    }
  }
  return (
    <div className='h-full overflow-hidden'>
      <div className='mb-5 flex items-center justify-between'>
        <h2 className={`text-[28px] font-semibold`}>{t('researchResults.researchDrafting')}</h2>
        {streamingComplete && !isFailed && (
          <>
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <ArrowDownTrayIcon
                    className='size-4 cursor-pointer'
                    onClick={onDownloadWord}></ArrowDownTrayIcon>
                </TooltipTrigger>
                <TooltipContent sideOffset={4}>
                  <div className='whitespace-pre-wrap text-sm leading-relaxed'>
                    {t('researchResults.downloadWord')}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </>
        )}
      </div>

      {/* 研究报告草稿区 */}
      <div
        className={`always-show-scrollbar report-container h-[calc(100%-60px)] overflow-y-auto pr-2`}
        style={{ scrollbarGutter: 'stable' }}>
        {(() => {
          // 如果失败，显示失败状态
          if (isFailed) {
            return renderFailedState()
          }

          // 如果没有大纲数据，显示loading
          if (!outline || outline.length === 0) {
            return renderLoadingState()
          }

          // 检查是否有任何章节内容
          const hasAnyContent = getAllChapterIndices(outline).some((chapterIndex) => {
            const contentItems = chapterContentItems.get(chapterIndex) || []
            return contentItems.some((item) => item.type === 'markdown' && item.content)
          })

          // 如果有大纲但没有任何章节内容，显示loading
          if (!hasAnyContent) {
            return (
              <div>
                {renderLoadingState()}
                {/* 推流进行时显示底部更新指示器 */}
                {isGlobalLoading && (
                  <div className='bg-card-50 sticky bottom-0 mt-4 flex items-center justify-center space-x-2 py-3 backdrop-blur-sm'>
                    <div className='flex items-center space-x-2'>
                      <div className='h-3 w-3 animate-pulse rounded-full bg-primary'></div>
                      <div className='h-3 w-3 animate-pulse rounded-full bg-primary delay-150'></div>
                      <div className='h-3 w-3 animate-pulse rounded-full bg-primary delay-300'></div>
                    </div>
                    <span className='ml-2 text-sm font-medium text-primary'>
                      {t('researchResults.updating')}
                    </span>
                  </div>
                )}
              </div>
            )
          }

          // 有大纲且有内容，显示章节内容
          return renderChapterContent()
        })()}
      </div>
    </div>
  )
}

export default ResearchDraftSection
