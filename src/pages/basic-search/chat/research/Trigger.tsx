import { Toolt<PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

const Trigger = ({
  children,
  section,
}: {
  children: React.ReactNode
  section: React.ReactNode
}) => {
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger>
          <div className='flex-center cursor-pointer text-primary'>{children}</div>
        </TooltipTrigger>
        <TooltipContent
          side='bottom'
          align='start'
          sideOffset={0}
          avoidCollisions={false}
          className='z-[1000] rounded-sm'>
          <div>{section}</div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
export default Trigger
