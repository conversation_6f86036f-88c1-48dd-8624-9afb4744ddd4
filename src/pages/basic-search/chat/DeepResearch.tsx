import React from 'react'
import { useRouter } from 'next/router'
import BasicSearchLayout from '../BasicSearchLayout'
import { Text, TextEnum } from '@/components/business/text'
import ResearchProcessSection from './research/ResearchProcessSection'
import ResearchDraftSection from './research/ResearchDraftSection'
import Trigger from './research/Trigger'
import OutlineSection from './research/OutlineSection'
import ResearchStatusIndicator from './components/ResearchStatusIndicator'
import ProgressDisplay from './components/ProgressDisplay'
// import NotificationDropdown from './components/NotificationDropdown'
import { useDeepResearchLogic } from './hooks/useDeepResearchLogic'
import { useResearchProgress } from './hooks/useResearchProgress'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
const DeepResearch: React.FC = () => {
  const router = useRouter()
  const { taskId } = router.query
  const { t } = useTranslation(Namespace.DEEPRESEARCH)

  // 使用自定义 hook 管理研究逻辑
  const {
    isGlobalLoading,
    researchStatus,
    streamingComplete,
    isFailed,
    researchMessages,
    chapterContentItems,
    outline,
    taskStatus,
    retryResearch,
  } = useDeepResearchLogic(taskId)

  // 计算进度
  const chapterProgress = useResearchProgress(outline, researchMessages, researchStatus)

  // 重试研究
  const onRetryDeepResearch = async () => {
    await retryResearch()
  }

  // 滚动到指定章节
  const scrollToChapter = (chapterIndex: string) => {
    console.log('🎯 Starting scroll to chapter:', chapterIndex)

    // 尝试滚动到研究过程面板的章节
    const processElementId = `process-chapter-${chapterIndex.replace(/\./g, '-')}`
    const processElement = document.getElementById(processElementId)

    // 尝试滚动到研究草稿面板的章节
    const draftElementId = `draft-chapter-${chapterIndex.replace(/\./g, '-')}`
    const draftElement = document.getElementById(draftElementId)

    console.log('🔍 Element search results:', {
      processElement: !!processElement,
      draftElement: !!draftElement,
      streamingComplete,
    })

    // 同时滚动两个面板到对应章节
    const scrollToElement = (element: HTMLElement) => {
      if (!element) return false

      // 获取滚动容器
      const scrollContainer = element.closest('.overflow-auto')

      if (scrollContainer) {
        // 计算目标元素相对于滚动容器的位置
        const containerRect = scrollContainer.getBoundingClientRect()
        const elementRect = element.getBoundingClientRect()
        const scrollTop = scrollContainer.scrollTop
        const targetScrollTop = scrollTop + elementRect.top - containerRect.top - 20 // 20px 偏移

        // 平滑滚动
        scrollContainer.scrollTo({
          top: targetScrollTop,
          behavior: 'smooth',
        })
      } else {
        // 回退到默认滚动
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        })
      }

      return true
    }

    // 尝试同时滚动两个面板（稍微错开时间以增强视觉效果）
    let scrolledCount = 0

    if (processElement) {
      if (scrollToElement(processElement)) {
        scrolledCount++
      }
    }

    // 稍微延迟草稿面板的滚动，让用户看到两个面板都在动
    if (draftElement) {
      setTimeout(() => {
        if (scrollToElement(draftElement)) {
          // 这里不增加 scrolledCount，因为 setTimeout 是异步的
        }
      }, 100) // 100ms 延迟
      scrolledCount++ // 但我们知道会滚动，所以计数
    }

    if (scrolledCount === 0) {
      console.warn('⚠️ No chapter elements found for:', chapterIndex)
    } else {
      console.log(`🎯 Successfully scrolled ${scrolledCount} panel(s) to chapter:`, chapterIndex)
    }
  }
  const renderStatusIcon = () => {
    let statusIcon = null
    // 使用 researchStatus 来判断是否完成，保持与标题状态指示器一致
    if (researchStatus === 'END' || chapterProgress.percentage === 100) {
      statusIcon = (
        <div className='flex h-4 w-4 items-center justify-center rounded-full bg-green-500'>
          <svg className='h-2.5 w-2.5 text-white' fill='currentColor' viewBox='0 0 20 20'>
            <path
              fillRule='evenodd'
              d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
              clipRule='evenodd'
            />
          </svg>
        </div>
      )
    } else {
      statusIcon = (
        <div
          className='h-4 w-4 animate-pulse rounded-full'
          style={{ background: 'rgba(249, 217, 1, 0.3)' }}>
          <div className='relative mr-2'>
            <div
              className='opacity-1 absolute left-1 top-1 h-2 w-2 rounded-full'
              style={{
                background: 'rgba(249, 217, 1, 1)',
              }}></div>
            <div className='absolute left-1 top-1 h-2 w-2 animate-ping rounded-full bg-current opacity-75'></div>
          </div>
        </div>
      )
    }
    return statusIcon
  }
  return (
    <BasicSearchLayout className='h-full'>
      <div className='flex h-full flex-col'>
        <header className='bg-card px-10 pb-6 pt-8'>
          <div className='flex items-center justify-between'>
            <div>
              <Text
                type={TextEnum.H4}
                className='mb-2 flex items-center justify-between !text-[20px] text-secondary-black-1'>
                {taskStatus.task?.question}
                <div className='flex w-48 justify-end'>
                  {/* <NotificationDropdown
                    onEmailNotificationToggle={(enabled) => {
                      console.log('Email notification:', enabled)
                    }}
                  /> */}
                  <ResearchStatusIndicator researchStatus={researchStatus} />
                </div>
              </Text>
              <Text type={TextEnum.Body_medium} className='!text-[13px] text-secondary-black-3'>
                {taskStatus.task?.requirement}
              </Text>
            </div>
          </div>
        </header>
        <main className='mx-3 mb-4 mt-3 flex h-full gap-4 overflow-hidden'>
          <div className='w-[60%] flex-1 shrink-0 pl-10 pt-5'>
            <Text type={TextEnum.H4} className='flex items-center justify-between pr-5'>
              <span>{t('researchResults.researchContent')}</span>
              <span className='flex items-center gap-1'>
                <ProgressDisplay progress={chapterProgress} />
                <Trigger
                  section={
                    <OutlineSection
                      outline={outline}
                      researchMessages={researchMessages}
                      researchStatus={researchStatus}
                      isFailed={isFailed}
                      onScrollToChapter={scrollToChapter}
                    />
                  }>
                  {renderStatusIcon()}
                </Trigger>
              </span>
            </Text>
            <div
              className='always-show-scrollbar mt-4 h-[calc(100%-40px)] overflow-y-auto pr-2'
              style={{ scrollbarGutter: 'stable' }}>
              <ResearchProcessSection
                outline={outline}
                researchMessages={researchMessages}
                chapterContentItems={chapterContentItems}
                isGlobalLoading={isGlobalLoading}
                researchStatus={researchStatus}
              />
            </div>
          </div>
          <div
            style={{
              boxShadow: '0px 0px 35px -3px rgba(0,0,0,0.1),0px 4px 6px -4px rgba(0,0,0,0.15);',
            }}
            className='w-[40%] flex-1 shrink-0 rounded-lg bg-card px-10 py-8'>
            <ResearchDraftSection
              taskId={taskId as string}
              outline={outline}
              chapterContentItems={chapterContentItems}
              isGlobalLoading={isGlobalLoading}
              researchStatus={researchStatus}
              streamingComplete={streamingComplete}
              isFailed={isFailed}
              onRetryDeepResearch={onRetryDeepResearch}
            />
          </div>
        </main>
      </div>
    </BasicSearchLayout>
  )
}
export default DeepResearch
