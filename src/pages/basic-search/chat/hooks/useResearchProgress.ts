import { useMemo } from 'react'
import { OutlineItem, ResearchMessage } from './useResearchState'

export interface ChapterProgress {
  completed: number
  total: number
  percentage: number
}

export const useResearchProgress = (
  outline: OutlineItem[],
  researchMessages: ResearchMessage[],
): ChapterProgress => {
  // 获取所有章节索引
  const getAllChapterIndices = (items: OutlineItem[]): string[] => {
    let indices: string[] = []

    items.forEach((item) => {
      if (item.chapterIndex) {
        indices.push(item.chapterIndex)
      }

      if (item.children && item.children.length > 0) {
        indices = [...indices, ...getAllChapterIndices(item.children)]
      }
    })
    return indices
  }

  // 计算已完成的章节数量
  const getCompletedChaptersCount = (items: OutlineItem[]): number => {
    const allChapterIndices = getAllChapterIndices(items)
    let completedCount = 0

    allChapterIndices.forEach((chapterIndex) => {
      // 检查该章节是否有完成的 REPORTING 消息
      const hasCompletedReporting = researchMessages.some(
        (message) =>
          message.ROLE === 'RESEARCHER' &&
          message.TYPE === 'REPORTING' &&
          message.STEP === 'END' &&
          message.CONTENT?.CHAPTER === chapterIndex,
      )

      if (hasCompletedReporting) {
        completedCount++
      }
    })

    return completedCount
  }

  // 使用 useMemo 优化计算
  const chapterProgress = useMemo((): ChapterProgress => {
    const totalChapters = getAllChapterIndices(outline).length
    const completedChapters = getCompletedChaptersCount(outline)

    return {
      completed: completedChapters,
      total: totalChapters,
      percentage: totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0,
    }
  }, [outline, researchMessages])

  return chapterProgress
}
