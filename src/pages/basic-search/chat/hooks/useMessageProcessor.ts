/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback } from 'react'
import { isJsonStr } from '@/lib/utils'
import { ResearchMessage, ChapterContentItem } from './useResearchState'

export const useMessageProcessor = () => {
  // 处理章节内容更新
  const updateChapterContents = useCallback(
    (
      researchMessages: ResearchMessage[],
      setChapterContentItems: (items: Map<string, ChapterContentItem[]>) => void,
    ) => {
      const newChapterContents = new Map<string, ResearchMessage[]>()
      const newChapterContentItems = new Map<string, ChapterContentItem[]>()

      console.log('🔄 Updating chapter contents, total messages:', researchMessages.length)

      // 先按章节分组所有消息
      researchMessages.forEach((message) => {
        if (message.CONTENT?.CHAPTER) {
          const chapter = message.CONTENT.CHAPTER
          const messages = newChapterContents.get(chapter) || []
          messages.push(message)
          newChapterContents.set(chapter, messages)
        }
      })

      // 处理每个章节的消息，将普通消息和Markdown内容交替排列
      newChapterContents.forEach((messages, chapter) => {
        const items: ChapterContentItem[] = []

        // 先处理非REPORTING类型的消息
        const regularMessages = messages.filter(
          (msg) =>
            !(
              msg.ROLE === 'RESEARCHER' &&
              msg.TYPE === 'REPORTING' &&
              ['START', 'RUNNING'].includes(msg.STEP)
            ),
        )

        // 将普通消息添加到内容项列表
        regularMessages.forEach((msg) => {
          items.push({
            type: 'message',
            message: msg,
          })
        })

        // 处理REPORTING类型的消息，合并成Markdown内容
        const reportingMessages = messages.filter(
          (msg) =>
            msg.ROLE === 'RESEARCHER' &&
            msg.TYPE === 'REPORTING' &&
            ['START', 'RUNNING'].includes(msg.STEP),
        )

        let markdownContent = ''
        let isStreaming = false

        // 合并所有REPORTING消息的内容，按时间顺序处理
        const sortedReportingMessages = reportingMessages.sort((a, b) => {
          // 如果没有时间戳，使用消息在数组中的位置
          const aIndex = messages.indexOf(a)
          const bIndex = messages.indexOf(b)
          return aIndex - bIndex
        })

        // 只使用最新的消息内容，避免重复累加
        if (sortedReportingMessages.length > 0) {
          const latestMessage = sortedReportingMessages[sortedReportingMessages.length - 1]
          if (latestMessage.CONTENT?.MESSAGE) {
            markdownContent = latestMessage.CONTENT.MESSAGE
          }
          // 如果最新消息的STEP不是END，则认为内容仍在流式传输
          if (latestMessage.STEP !== 'END') {
            isStreaming = true
          }
        }

        // 如果有Markdown内容，则添加到内容项列表
        if (markdownContent) {
          items.push({
            type: 'markdown',
            content: markdownContent,
            isStreaming: isStreaming,
          })
        }

        // 更新章节内容项映射
        newChapterContentItems.set(chapter, items)
      })

      console.log('📊 Chapter content items updated:', {
        totalChapters: newChapterContentItems.size,
        chapters: Array.from(newChapterContentItems.keys()),
      })

      setChapterContentItems(newChapterContentItems)
    },
    [],
  )

  // 验证消息是否有效
  const validateMessage = useCallback(
    (
      message: any,
      researchDataRef: React.RefObject<any>,
      outlineRef: React.RefObject<any[]>,
    ): boolean => {
      // 处理大纲确认消息
      if (message.type === 'confirm') {
        console.log('✅ Confirm message validated')
        return true
      }

      // 采用严格的过滤逻辑
      const hasResearchData = !!researchDataRef.current
      const hasTaskId = !!researchDataRef.current?.task_id
      const hasOutline = outlineRef.current?.length > 0
      const isValidJson = isJsonStr(message.data)
      const taskIdMatches = message.task_id === researchDataRef.current?.task_id

      console.log('🔍 Message validation:', {
        hasResearchData,
        hasTaskId,
        hasOutline,
        isValidJson,
        taskIdMatches,
        messageTaskId: message.task_id,
        currentTaskId: researchDataRef.current?.task_id,
      })

      if (!hasResearchData || !hasTaskId || !hasOutline || !isValidJson || !taskIdMatches) {
        console.log('❌ Message validation failed')
        return false
      }

      console.log('✅ Message validation passed')
      return true
    },
    [],
  )

  // 处理状态消息
  const processStatusMessage = useCallback(
    (
      messageData: ResearchMessage,
      setIsGlobalLoading: (loading: boolean) => void,
      setStreamingComplete: (complete: boolean) => void,
      setShowToast: (show: boolean) => void,
      setIsFailed: (failed: boolean) => void,
      setResearchStatus: (status: string) => void,
    ) => {
      if (messageData.ROLE === 'WORKFLOW' && messageData.TYPE === 'STATUS' && messageData.STEP) {
        const newStep = messageData.STEP
        console.log('🔄 Processing status message:', {
          ROLE: messageData.ROLE,
          TYPE: messageData.TYPE,
          STEP: newStep,
        })

        // 每个状态转换都需要保持loading状态，除非是END
        if (newStep === 'END') {
          // 研究结束时关闭loading并显示toast
          setIsGlobalLoading(false)
          setStreamingComplete(true)
          setShowToast(true)
          setTimeout(() => {
            setShowToast(false)
          }, 3000)
        } else if (newStep === 'FAILED') {
          setIsFailed(true)
          // 研究结束时关闭loading并显示toast
          setIsGlobalLoading(false)
          setStreamingComplete(true)
        } else {
          // 其他状态都保持loading
          setIsGlobalLoading(true)
        }

        // 更新当前研究状态
        console.log('🎯 Setting research status to:', newStep)
        setResearchStatus(newStep)
      } else {
        console.log('⚠️ Message does not match status criteria:', {
          ROLE: messageData.ROLE,
          TYPE: messageData.TYPE,
          STEP: messageData.STEP,
        })
      }
    },
    [],
  )

  return {
    updateChapterContents,
    validateMessage,
    processStatusMessage,
  }
}
