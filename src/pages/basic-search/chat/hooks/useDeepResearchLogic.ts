/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useCallback, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Namespace } from '@/i18n'
import { useDeepResearchStore } from '@/store/deep-research'
import useWebSocketWithReconnection from '@/pages/deep-explore/socket'
import { useResearchState } from './useResearchState'
import { useMessageProcessor } from './useMessageProcessor'
import { taskRetryApi } from '@/api/deep-research'

export const useDeepResearchLogic = (taskId: string | string[] | undefined) => {
  const { t } = useTranslation([Namespace.BASIC_SEARCH, Namespace.GLOBAL])
  const { getTaskStatus, taskStatus } = useDeepResearchStore()

  const {
    isGlobalLoading,
    setIsGlobalLoading,
    researchStatus,
    setResearchStatus,
    streamingComplete,
    setStreamingComplete,
    isFailed,
    setIsFailed,
    showToast,
    setShowToast,
    researchMessages,
    chapterContentItems,
    setChapterContentItems,
    outline,
    researchDataRef,
    outlineRef,
    streamRequestSentRef,
    setResearchData,
    setOutlineData,
    addResearchMessage,
    addResearchMessages,
    resetState,
  } = useResearchState()

  const { updateChapterContents, validateMessage, processStatusMessage } = useMessageProcessor()

  // 初始化任务状态
  const initStatus = useCallback(
    async (id: string) => {
      if (!taskId) return

      try {
        const res = await getTaskStatus(id)
        const taskData = res.data.task

        // 设置任务数据到 ref
        setResearchData({
          task_id: taskData.task_id,
          question: taskData.question,
          requirement: taskData.requirement || '',
        })

        // 设置大纲数据到 ref 和 state
        if (taskData.outline && Array.isArray(taskData.outline)) {
          setOutlineData(taskData.outline)
        }

        if (res.data.task.status === 'FAILED') {
          setIsFailed(true)
          setResearchStatus('FAILED')
        }

        // 处理历史消息
        if (taskData?.record && taskData.record.length > 0) {
          let latestStatus = 'START'
          const messages = taskData.record.map((msg: any) => {
            // 跟踪最新的工作流状态
            if (msg.ROLE === 'WORKFLOW' && msg.TYPE === 'STATUS' && msg.STEP) {
              latestStatus = msg.STEP
              console.log('📝 Found status message in history:', msg.STEP)
            }

            if (msg.STEP === 'END') {
              // 研究结束时关闭loading并显示toast
              setIsGlobalLoading(false)
              setStreamingComplete(true)
              setShowToast(true)
              setTimeout(() => {
                setShowToast(false)
              }, 3000)
            }
            return msg
          })

          // 设置最新的研究状态
          console.log(
            '🚀 Initializing with latest status:',
            latestStatus,
            'from',
            messages.length,
            'messages',
          )
          setResearchStatus(latestStatus)
          addResearchMessages(messages)
        } else {
          console.log('📭 No historical messages found, keeping initial status')
        }
      } catch (error) {
        console.error('Failed to initialize task status:', error)
        setIsFailed(true)
      }
    },
    [getTaskStatus], // 移除 taskId 依赖，避免循环依赖
  )

  // 处理 WebSocket 消息
  const handleSocketMessage = useCallback(
    (message: any) => {
      try {
        // 处理大纲确认消息
        if (message.type === 'confirm') {
          const outline = message.data.message['报告大纲']
          setOutlineData(outline)
          return
        }

        // 验证消息
        if (!validateMessage(message, researchDataRef, outlineRef)) {
          return
        }

        streamRequestSentRef.current = true
        setIsGlobalLoading(true)
        // 移除强制设置为 'START'，让 processStatusMessage 处理状态

        // 提取JSON部分
        const messageData = JSON.parse(message.data)

        // 处理状态消息
        processStatusMessage(
          messageData,
          setIsGlobalLoading,
          setStreamingComplete,
          setShowToast,
          setIsFailed,
          setResearchStatus,
        )

        // 添加消息到状态
        console.log('📨 Adding new message:', {
          ROLE: messageData.ROLE,
          TYPE: messageData.TYPE,
          STEP: messageData.STEP,
          CHAPTER: messageData.CONTENT?.CHAPTER,
        })
        addResearchMessage(messageData)
      } catch (error) {
        console.error(t('researchResults.errorOccurred'), error)
        setIsGlobalLoading(false)
      }
    },
    [validateMessage, processStatusMessage, setOutlineData, addResearchMessage, t], // 简化依赖数组
  )

  // 重试研究功能
  const retryResearch = useCallback(async () => {
    if (!taskId) {
      console.warn('⚠️ No taskId provided for retry')
      return
    }

    try {
      console.log('🔄 Retrying research for task:', taskId)

      // 重置相关状态，但保留任务数据和大纲
      setIsFailed(false)
      setResearchStatus('START')
      setStreamingComplete(false)
      setIsGlobalLoading(true)
      setShowToast(false)

      // 清空之前的研究消息和章节内容，准备接收新的消息
      // 注意：保留 researchDataRef 和 outlineRef，因为它们包含任务基础信息
      // 使用部分重置，只清空消息相关的状态
      const currentResearchData = researchDataRef.current
      const currentOutline = outlineRef.current

      resetState() // 完全重置状态

      // 恢复任务数据和大纲
      if (currentResearchData) {
        setResearchData(currentResearchData)
      }
      if (currentOutline && currentOutline.length > 0) {
        setOutlineData(currentOutline)
      }

      // 调用重试 API
      await taskRetryApi({ taskId: taskId as string })

      console.log('✅ Retry API call successful, waiting for new messages...')
    } catch (error) {
      console.error('❌ Retry failed:', error)
      setIsFailed(true)
      setIsGlobalLoading(false)
      setResearchStatus('FAILED')
    }
  }, [
    taskId,
    setIsFailed,
    setResearchStatus,
    setStreamingComplete,
    setIsGlobalLoading,
    setShowToast,
  ])

  // WebSocket 连接
  const { startWebSocket } = useWebSocketWithReconnection({
    onMessage: handleSocketMessage,
  })

  // 使用 ref 来避免 startWebSocket 的依赖问题
  const startWebSocketRef = useRef(startWebSocket)
  startWebSocketRef.current = startWebSocket

  // 初始化效果
  useEffect(() => {
    startWebSocketRef.current()
  }, []) // 只在组件挂载时初始化一次

  // 任务状态初始化效果
  useEffect(() => {
    if (taskId) {
      initStatus(taskId as string)
    }
  }, [taskId, initStatus]) // 包含 initStatus 依赖

  // 监控研究状态变化
  useEffect(() => {
    console.log('📊 Research status changed to:', researchStatus)
  }, [researchStatus])

  // 章节内容更新回调
  const handleChapterContentsUpdate = useCallback(() => {
    console.log(
      '🔄 Chapter contents update triggered, researchMessages length:',
      researchMessages.length,
    )
    updateChapterContents(researchMessages, setChapterContentItems)
  }, [researchMessages, updateChapterContents, setChapterContentItems])

  // 更新章节内容效果
  useEffect(() => {
    handleChapterContentsUpdate()
  }, [handleChapterContentsUpdate])

  return {
    // 状态
    isGlobalLoading,
    researchStatus,
    streamingComplete,
    isFailed,
    showToast,
    researchMessages,
    chapterContentItems,
    outline,
    taskStatus,
    // 方法
    retryResearch,
  }
}
