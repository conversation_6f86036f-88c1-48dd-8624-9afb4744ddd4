@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #f4f7fd;
    --foreground: rgb(9, 9, 11); // #09090b;
    --foreground-rgb: 9, 9, 11; // #09090b;

    --card: rgb(255, 255, 255); // #ffffff
    --card-rgb: 255, 255, 255; // #ffffff
    --card-foreground: rgb(9, 9, 11);

    --popover: rgb(255, 255, 255);
    --popover-foreground: rgb(9, 9, 11);

    --tooltip: rgb(9, 9, 11); // #09090b
    --tooltip-foreground: rgb(255, 255, 255);

    --primary: #5661f6;
    --primary-foreground: rgb(255, 255, 255);

    --primary-darker: #4240eb;
    --primary-lighter: #7cbefd;
    --primary-disabled: #c5d4ff;
    --primary-hover: rgba(86, 97, 246, 0.1);
    --primary-bg: #f4f7fd;
    --primary-300: #a2b6ff;

    --secondary: #ebeef5;
    --secondary-foreground: #09090b;

    --secondary-text-1: #09090b;
    --secondary-text-2: #424962;
    --secondary-text-3: #848fac;

    --secondary-active: #d4d7e3;
    --secondary-hover: #e3e5ee;
    --secondary-disabled: #f5f6f8;

    --muted: #f5f6f8;
    --muted-foreground: #b6bed3;

    --accent: #ebeeef;
    --accent-foreground: rgb(9, 9, 11);

    --modal-in-card: #f4f7fd;

    --destructive: #ee2f2f;
    --destructive-foreground: rgb(255, 255, 255);

    --border: #e4e5ec;
    --input: rgb(255, 255, 255);
    --ring: #5661f6;

    --success: #34a853;
    --success-rgb: 52, 168, 83;

    --dangerous: #ee2f2f;
    --dangerous-rgb: 238, 47, 47;
    --dangerous-darker: #dd2525;
    --dangerous-lighter: #f97070;
    --dangerous-disabled: #fecaca;

    --warning: #FF9A26;

    --info: #2b79ff;
    --info-rgb: 43, 121, 255;

    --black-1: rgb(9, 9, 11);
    --black-1-rgb: 9, 9, 11;
    --black-2: rgb(57, 57, 57);

    --gray: #e0e0e9;
    --gray-tab-header: rgb(249, 250, 251);
    --gray-img-bg: rgba(230, 230, 230, 1);
    --gray-skeleton: rgb(234, 239, 247);
  }

  .dark {
    --background: rgb(14, 14, 17); // #0E0E11
    --foreground: rgb(239, 239, 248); // #EFEFF8
    --foreground-rgb: 239, 239, 248; // #09090b;

    --card: rgb(25, 25, 28); // #19191C
    --card-rgb: 25, 25, 28;
    --card-foreground: rgb(239, 239, 248); //#EFEFF8

    --popover: rgb(25, 25, 28); // #19191C
    --popover-foreground: rgb(239, 239, 248); //#EFEFF8

    --tooltip: rgb(9, 9, 11);
    --tooltip-foreground: rgb(255, 255, 255);

    --primary: #5661f6;
    --primary-foreground: rgb(239, 239, 248); // #EFEFF8

    --primary-dark: #4240eb;
    --primary-lighter: #7c8efd;
    --primary-disabled: #c5d4ff; // todo 跟前景色太接近了
    --primary-hover: rgba(86, 97, 246, 0.1);
    --primary-bg: rgb(25, 25, 28);
    --primary-300: #a2b6ff;

    --secondary: rgb(40, 40, 47); // #28282F
    --secondary-foreground: rgb(239, 239, 248); //#EFEFF8

    --secondary-text-1: rgb(239, 239, 248); //#EFEFF8
    --secondary-text-2: rgb(186, 190, 204); //#BABECC
    --secondary-text-3: rgb(132, 143, 172); //#848FAC

    --secondary-active: rgb(239, 239, 248);
    --secondary-hover: rgb(40, 40, 47);
    --secondary-disabled: rgb(132, 143, 172);

    --muted: rgb(43, 43, 49); //#2B2B31
    --muted-foreground: rgb(65, 65, 74); // #41414A

    --accent: rgb(40, 40, 47); // #28282F
    --accent-foreground: rgb(239, 239, 248);

    --modal-in-card: rgb(40, 40, 47);

    --destructive: #ee2f2f;
    --destructive-foreground: rgb(239, 239, 248); // #EFEFF8

    --border: #2a2c33;
    --input: #2a2c33;
    --ring: #5661f6;

    --success: #34a853;
    --success-rgb: 52, 168, 83;

    --dangerous: #ee2f2f;
    --dangerous-rgb: 238, 47, 47;
    --dangerous-darker: #dd2525;
    --dangerous-lighter: #f97070;
    --dangerous-disabled: #fecaca;

    --info: #2b79ff;
    --info-rgb: 43, 121, 255;

    --black-1: rgb(9, 9, 11);
    --black-1-rgb: 9, 9, 11;
    --black-2: rgb(57, 57, 57);

    --gray: #e0e0e9;
    --gray-tab-header: rgb(14, 14, 17);
    --gray-img-bg: rgba(230, 230, 230, 1);
    --gray-skeleton: rgb(234, 239, 247, 0.2);
  }
}

@layer base {
  * {
    @apply border-border;
    box-sizing: border-box;
  }
  body {
    @apply bg-background text-foreground;
  }
  html {
    font-size: 16px;
  }
  * {
    font-family: var(--font-roboto);
  }
}

@layer utilities {
  .hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  .hide-scrollbar::-webkit-scrollbar {
    width: 0; /* Webkit browsers (Chrome, Safari, Edge) */
    height: 0; /* Hide horizontal scrollbar */
  }

  .custom-scrollbar {
    /* 默认隐藏滚动条 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 0; /* Webkit (Chrome, Safari, Edge) */
  }

  /* 当滚动时显示滚动条 */
  .custom-scrollbar:hover {
    scrollbar-width: thin; /* Firefox */
  }

  .custom-scrollbar:hover::-webkit-scrollbar {
    width: 8px; /* Webkit (Chrome, Safari, Edge) */
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2); /* 滑块的颜色 */
    border-radius: 4px; /* 滑块的圆角 */
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.1); /* 轨道的颜色 */
  }
}

@layer components {
  /* 更多自定义样式组合 */
  .bg-image-custom {
    @apply bg-cover bg-center bg-no-repeat;
  }
  .flex-center {
    @apply flex items-center justify-center;
  }
  .flex-h-center {
    @apply flex justify-center;
  }
  .flex-v-center {
    @apply flex items-center;
  }

  .ai-smarties-color-bg {
    background: linear-gradient(
      135deg,
      rgb(206, 138, 255) 7.773%,
      rgb(117, 182, 251) 65.649%,
      rgb(86, 97, 246) 94.96%
    );
  }

  /* markdown 插件 自定义样式 - start*/
  .ai-smarties-md-item {
    position: relative;
    display: inline-block;
  }

  .ai-smarties-md-item-trigger {
    position: relative; /* The tooltip will be positioned relative to this */
  }

  .ai-smarties-md-item-content {
    position: absolute;
    left: calc(100% + 8px); /* Place to the right of the trigger with 8px spacing */
    top: 50%;
    transform: translateY(-50%); /* Center align with the trigger vertically */
    padding: 8px;
    z-index: 999; /* Ensure the popover is on top of other content */
    display: none; /* Initially hidden */

    background-color: var(--card); /* Background color of the popover */
    border: 1px solid var(--border);
    border-radius: 6px;
    box-shadow:
      0px 4px 6px -4px rgba(0, 0, 0, 0.1),
      0px 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
  .ai-smarties-md-title {
    font-size: 14px;
  }

  .ai-smarties-md-item-content.visible {
    display: block; /* Show when visible */
  }

  .ai-smarties-md-text {
    word-break: break-word;
    a {
      color: var(--primary);
    }
    h1 {
      font-size: 1.5rem; /* 缩小到 1.5rem */
      line-height: 2.25rem; /* 行高与字体比例适中 */
      font-weight: 600;
    }
    h2 {
      font-size: 1.25rem; /* 缩小到 1.25rem */
      line-height: 2rem; /* 行高与字体比例适中 */
      font-weight: 600;
    }
    h3 {
      font-size: 1rem; /* 缩小到 1rem */
      line-height: 1.75rem; /* 行高与字体比例适中 */
      font-weight: 600;
    }
    h4 {
      font-size: 0.875rem; /* 缩小到 0.875rem */
      line-height: 1.5rem; /* 行高与字体比例适中 */
      font-weight: 500;
    }
    h5 {
      font-size: 0.75rem; /* 缩小到 0.75rem */
      line-height: 1.25rem; /* 行高与字体比例适中 */
      font-weight: 500;
    }
    h6 {
      font-size: 0.625rem; /* 缩小到 0.625rem */
      line-height: 1rem; /* 行高与字体比例适中 */
      font-weight: 500;
    }
    blockquote,
    dl,
    dd,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    hr,
    figure,
    p,
    pre {
      margin: 4px 0;
    }

    pre {
      background-color: #2d2d2d; /* 背景色，类似Markdown的代码块 */
      color: #f8f8f2; /* 代码块文字颜色 */
      padding: 16px; /* 内边距，增加代码块的内填充 */
      border-radius: 6px; /* 圆角，使代码块更柔和 */
      overflow-x: auto; /* 当代码超出容器宽度时，横向滚动 */
      font-size: 14px; /* 字体大小 */
      line-height: 1.6; /* 行高，增加代码块的可读性 */
      margin: 16px 0; /* 上下外边距，增加代码块的间距 */
      border: 1px solid #44475a; /* 细线边框，与背景色协调 */
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 阴影效果，使代码块突出 */
    }

    pre code {
      background-color: transparent; /* 透明背景，保持与pre一致 */
      color: inherit; /* 继承颜色 */
      font-family: 'Fira Code', monospace; /* 使用等宽字体 */
      white-space: pre-wrap; /* 允许自动换行 */
      word-wrap: break-word; /* 打断长单词 */

      font-size: 0.75rem;
      line-height: 1.25rem;
    }

    fieldset {
      margin: 4px;
      padding: 4px;
    }

    legend {
      padding: 4px;
    }

    ul {
      list-style: disc outside none;
      margin: 16px 0;
      padding-left: 40px;
    }

    ol {
      list-style: decimal outside none;
      margin: 16px 0;
      padding-left: 40px;
    }

    dialog {
      padding: 4px;
    }

    /* Table default styles */
    table {
      width: 100%;
      border: 1px solid var(--border); /* Outer table border */
      background: var(--card);
    }

    thead th {
      padding: 8px;
      text-align: left;
      font-weight: bold;
    }

    tbody td {
      padding: 8px;
      border: 1px solid var(--border);
      text-align: left;
    }

    th,
    td {
      border: 1px solid var(--border); /* Cell borders */
    }

    th {
      font-size: 0.875rem;
      line-height: 1.5rem;
    }

    td {
      font-size: 0.875rem;
      line-height: 1.5rem;
    }
  }
  /* markdown 插件 自定义样式 - end */
  .fadeOut {
    transition: transform 0.3s;
    transform: rotateX(-90deg);
  }
  .fadeRight {
    transition: transform 0.3s;
    transform: rotateY(-90deg);
  }
}
