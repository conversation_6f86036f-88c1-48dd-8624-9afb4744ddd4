import clsx from 'clsx'
import React, { useEffect } from 'react'
import { withPortal, isServer } from '../hoc/WithPortal'
import { XMarkIcon } from '@heroicons/react/24/solid'
export interface ModalProps {
  open?: boolean
  onClose?: () => void
  children?: React.ReactNode
  scrollable?: boolean
  showCloseIcon?: boolean
  overlayClassName?: string | string[]
  className?: string | string[]
  overlayClose?: boolean
}

export const Modal: React.FC<ModalProps> = withPortal(
  ({
    open,
    onClose,
    children,
    overlayClassName,
    className,
    showCloseIcon,
    overlayClose = true,
  }: ModalProps) => {
    const handleClose = () => {
      if (onClose) onClose()
    }

    const handleClickModal = (e: React.MouseEvent<HTMLDivElement>) => {
      e.stopPropagation()
    }

    useEffect(() => {
      if (!open || isServer()) return

      document.body.style.overflow = 'hidden'

      return () => {
        document.body.style.overflow = 'auto'
      }
    }, [open])

    if (!open) {
      return <></>
    }

    return (
      <div
        className={clsx(
          'flex-center fixed bottom-0 left-0 right-0 top-0 bg-black-1-70',
          overlayClassName,
        )}
        onClick={overlayClose ? handleClose : undefined}>
        <div
          className={clsx('relative max-w-full rounded-sm bg-card', className)}
          onClick={handleClickModal}>
          {showCloseIcon && (
            <XMarkIcon
              className='absolute right-[10px] top-[10px] h-6 w-6 cursor-pointer'
              onClick={handleClose}
            />
          )}
          {children}
        </div>
      </div>
    )
  },
  'modal',
)
