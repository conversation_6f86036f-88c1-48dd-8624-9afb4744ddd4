import React, {
  use<PERSON><PERSON>back,
  useEffect,
  useImperative<PERSON><PERSON><PERSON>,
  useMemo,
  useRef,
  useState,
} from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { Text, TextEnum } from '../text'
import {
  ChatBubbleLeftRightIcon,
  FolderIcon,
  PlusIcon,
  TrashIcon,
  EllipsisHorizontalIcon,
  PencilSquareIcon,
} from '@heroicons/react/24/outline'
import { Input } from '@/components/ui/input'
import clsx from 'clsx'
import { Namespace } from '@/i18n'
import { useTranslation } from 'react-i18next'
import { PoTypeEnum } from '@/types'
import { useRouter } from 'next/router'
import { smartiesRoutes } from '@/routers'
import { deleteSession } from '@/api/deleteSession'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import Image from 'next/image'
import { getSessionList } from '@/api/getSessionList'
import { updateSession } from '@/api/updateSession'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import DeleteMenuModal from './DeleteMenuModal'
import { Skeleton } from '@/components/ui/skeleton'
import { EventBus$ } from '@/eventbus'

interface PaginationInfo {
  lastPageKey: string | null
  pageSize: number
}

export interface SubMenuItem {
  sessionId: string
  title: string
}
export interface MenuItems {
  [key: string]: {
    items: SubMenuItem[]
    pagination: PaginationInfo
    loading: boolean
  }
}

export interface MenuProps {
  className?: string
}

// 定义子组件暴露的接口类型
export interface MenuComponentHandles {
  updateSecondMenu: (menuName: PoTypeEnum) => void
}

const SubMenuSkeleton = () => {
  const list = new Array(5).fill('')
  return (
    <>
      {list.map((_, idx) => (
        <div className='flex gap-2 px-3 py-2' key={idx}>
          <Skeleton className='h-4 w-4 rounded' />
          <Skeleton className='h-4 flex-1 rounded' />
        </div>
      ))}
    </>
  )
}

interface IMenuItemTitleProps {
  menu: PoTypeEnum
  item: SubMenuItem
  activeItem?: {
    menu: PoTypeEnum
    item: SubMenuItem
  }
  onClickChat?: (menu: PoTypeEnum, item: SubMenuItem) => void
  onConfirmRename?: (menu: PoTypeEnum, item: SubMenuItem, renameTitle: string) => void
  onClickDelete?: (menu: PoTypeEnum, item: SubMenuItem) => void
}

const MenuItemTitle = (props: IMenuItemTitleProps) => {
  const { menu, item, activeItem, onClickChat, onClickDelete, onConfirmRename } = props
  const [edit, setEdit] = useState(false)
  const [renameTitle, setRenameTitle] = useState(item.title)
  const [dropdownVisible, setDropdownVisible] = useState(false)
  const { t } = useTranslation(Namespace.GLOBAL)
  const inputWrapRef = useRef<HTMLDivElement>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const triggerRef = useRef<HTMLDivElement>(null)
  const active = useMemo(
    () =>
      !!(
        activeItem?.menu.toLocaleLowerCase() === menu.toLocaleLowerCase() &&
        activeItem.item.sessionId === item.sessionId
      ),
    [activeItem, item],
  )

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        triggerRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !triggerRef.current.contains(event.target as Node)
      ) {
        setDropdownVisible(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const renameClickOutside = useCallback(
    (event: MouseEvent) => {
      if (inputWrapRef.current && !inputWrapRef.current.contains(event.target as Node)) {
        setEdit(false)
        if (renameTitle !== item.title && renameTitle) {
          onConfirmRename?.(menu, item, renameTitle)
        }
      }
    },
    [onConfirmRename, menu, item, renameTitle],
  )

  useEffect(() => {
    document.addEventListener('mousedown', renameClickOutside)
    return () => {
      document.removeEventListener('mousedown', renameClickOutside)
    }
  }, [renameClickOutside])

  return (
    <div
      className={clsx('flex-v-center group my-0.5 rounded-sm', {
        'hover:bg-primary-hover': !active, // 仅当不 active 时启用 hover 样式 防止颜色叠加多次
        'bg-primary-hover': active || dropdownVisible, // active 在父元素应用，不需要每个元素添加
      })}>
      <TooltipProvider delayDuration={0} data-component='MenuItemTitle'>
        <Tooltip>
          <TooltipTrigger
            data-component='TooltipTrigger'
            data-state='instant-open'
            className='flex-1 overflow-hidden text-ellipsis whitespace-nowrap rounded-l-sm text-left'>
            {edit ? (
              <div ref={inputWrapRef}>
                <Input
                  value={renameTitle}
                  type='text'
                  onChange={(event) => {
                    setRenameTitle(event.target.value)
                  }}
                  autoFocus
                  onKeyDown={(event) => {
                    if (event.key === 'Enter') {
                      setEdit(false)
                      onConfirmRename?.(menu, item, renameTitle)
                    }
                  }}
                />
              </div>
            ) : (
              <div
                className={clsx('flex-v-center h-10 rounded-sm p-3', {
                  // 'bg-primary-hover': active,
                })}
                data-component='MenuItemTitle'
                onClick={() => {
                  onClickChat?.(menu, item)
                }}>
                <ChatBubbleLeftRightIcon className='mr-2 h-4 w-4 shrink-0' />
                <Text
                  type={TextEnum.Body_medium}
                  className='flex-1 overflow-hidden text-ellipsis whitespace-nowrap text-left'>
                  {item.title}
                </Text>
              </div>
            )}
          </TooltipTrigger>
          <TooltipContent
            side='right'
            sideOffset={36}
            className='rounded-sm border-none bg-tooltip'>
            <div className='max-h-[74px] max-w-80 overflow-y-hidden text-ellipsis p-2 text-left text-tooltip-foreground'>
              <Text type={TextEnum.Body_small}>{edit ? renameTitle : item.title}</Text>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      {edit ? null : (
        <div ref={triggerRef}>
          <DropdownMenu modal={false} open={dropdownVisible}>
            <DropdownMenuTrigger asChild>
              <div
                className={clsx(
                  'group-hover:flex-center invisible h-10 cursor-pointer rounded-r-sm pl-2 group-hover:visible',
                  {
                    'flex-center': dropdownVisible,
                    visible: dropdownVisible,
                  },
                )}
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  setDropdownVisible((prev) => !prev)
                }}>
                <EllipsisHorizontalIcon className='mr-2 h-4 w-4 shrink-0' />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className='w-44' ref={dropdownRef}>
              <DropdownMenuItem
                className='h-9'
                onClick={() => {
                  setDropdownVisible(false)
                  setEdit(true)
                }}>
                <PencilSquareIcon
                  width={16}
                  height={16}
                  className='mr-2 h-4 w-4 shrink-0 text-secondary-black-2'
                />
                <Text className={clsx('text-secondary-black-2')} type={TextEnum.Body_medium}>
                  {t('sideMore.rename')}
                </Text>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  setDropdownVisible(false)
                  onClickDelete?.(menu, item)
                }}
                className='h-9'>
                <TrashIcon
                  width={16}
                  height={16}
                  fontSize={16}
                  className='mr-2 h-4 w-4 shrink-0 text-secondary-black-2'
                />
                <Text className={clsx('text-secondary-black-2')} type={TextEnum.Body_medium}>
                  {t('sideMore.delete')}
                </Text>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
    </div>
  )
}

const Menu = React.forwardRef<MenuComponentHandles, MenuProps>((props, ref) => {
  const { className } = props

  const { t } = useTranslation(Namespace.GLOBAL)
  const router = useRouter()

  const [openDeleteModal, setOpenDeleteModal] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState<
    | {
        menu: PoTypeEnum
        item: SubMenuItem
      }
    | undefined
  >(undefined)

  const [expandedMenus, setExpandedMenus] = useState<PoTypeEnum[]>([])

  const [secondMenuItems, setSecondMenuItems] = useState<MenuItems>({
    [PoTypeEnum.MARKET]: {
      items: [],
      pagination: { lastPageKey: null, pageSize: 10 },
      loading: false,
    },
    [PoTypeEnum.TOPIC]: {
      items: [],
      pagination: { lastPageKey: null, pageSize: 10 },
      loading: false,
    },
    [PoTypeEnum.SWOT]: {
      items: [],
      pagination: { lastPageKey: null, pageSize: 10 },
      loading: false,
    },
    [PoTypeEnum.COMPANY]: {
      items: [],
      pagination: { lastPageKey: null, pageSize: 10 },
      loading: false,
    },
    [PoTypeEnum.REGULATION]: {
      items: [],
      pagination: { lastPageKey: null, pageSize: 10 },
      loading: false,
    },
    [PoTypeEnum.RISK]: {
      items: [],
      pagination: { lastPageKey: null, pageSize: 10 },
      loading: false,
    },
    [PoTypeEnum.GENERAL]: {
      items: [],
      pagination: { lastPageKey: null, pageSize: 10 },
      loading: false,
    },
    [PoTypeEnum.DEEPRESEARCH]: {
      items: [],
      pagination: { lastPageKey: null, pageSize: 10 },
      loading: false,
    },
  })

  const toggleMenu = (menu: PoTypeEnum) => {
    if (expandedMenus.includes(menu)) {
      setExpandedMenus(expandedMenus.filter((m) => m !== menu))
    } else {
      setExpandedMenus([...expandedMenus, menu])
      // 打开就重新请求 二级菜单
      setSecondMenuItems((prevState) => ({
        ...prevState,
        [menu]: {
          ...prevState[menu],
          loading: true,
        },
      }))
      updateSubmenuList(menu, true)
    }
  }

  const updateSubmenuList = async (menu: PoTypeEnum, isInitialLoad = false) => {
    // 添加加载更多的逻辑
    const { lastPageKey, pageSize } = secondMenuItems[menu].pagination
    const currentlastPageKey = isInitialLoad ? '' : lastPageKey

    const data = await getSessionList({
      poType: PoTypeEnum[menu] as unknown as PoTypeEnum,
      pageSize,
      lastPageKey: currentlastPageKey ?? undefined,
    })

    const newItems = (data.items ?? []).map((item) => {
      return {
        sessionId: item.sessionId,
        title: item.title,
      }
    })
    const newlastPageKey = data.pageKey || null

    if (isInitialLoad) {
      setSecondMenuItems((prevState) => ({
        ...prevState,
        [menu]: {
          items: newItems,
          pagination: { lastPageKey: newlastPageKey, pageSize },
          loading: false,
        },
      }))
    } else {
      setSecondMenuItems((prevState) => ({
        ...prevState,
        [menu]: {
          items: [...prevState[menu].items, ...newItems],
          pagination: { lastPageKey: newlastPageKey, pageSize },
          loading: false,
        },
      }))
    }
  }

  const [activeItem, setActiveItem] = useState<
    | {
        menu: PoTypeEnum
        item: SubMenuItem
      }
    | undefined
  >(undefined)

  useEffect(() => {
    const { type, sessionId } = router.query

    if (type && sessionId) {
      const menu = ((type as string) ?? '').toLocaleLowerCase() as PoTypeEnum

      setActiveItem({
        menu: type as PoTypeEnum,
        item: {
          title: '',
          sessionId: sessionId as string,
        },
      })
      // 如果一级菜单没打开，打开一级菜单
      if (!expandedMenus.includes(menu.toLocaleUpperCase() as PoTypeEnum)) {
        toggleMenu(menu.toLocaleUpperCase() as PoTypeEnum)
      }
    } else {
      // console.log('111111', router)
      // 都没有的默认打开第一个
      // toggleMenu(type?.toLocaleUpperCase() as PoTypeEnum)
    }
  }, [router.query])

  const handleClickChat = (menu: PoTypeEnum, item: SubMenuItem) => {
    setActiveItem({
      menu,
      item,
    })
    router.replace(
      {
        pathname: smartiesRoutes.basicSearch.chat(menu.toLocaleLowerCase()),
        query: {
          sessionId: item.sessionId,
        },
      },
      undefined,
      { shallow: true },
    )
  }

  const handleDeleteChat = async () => {
    // menu: PoTypeEnum, item: SubMenuItem
    if (deleteTarget) {
      const { menu, item } = deleteTarget

      const delRes = await deleteSession({
        sessionId: item.sessionId,
      })
      setOpenDeleteModal(false)
      if (delRes.result === 'ok') {
        // 如果当前页面就是这个会话，就回到首页
        // 否则不动
        // 打开就重新请求 二级菜单
        setSecondMenuItems((prevState) => ({
          ...prevState,
          [menu]: {
            ...prevState[menu],
            loading: true,
          },
        }))
        updateSubmenuList(menu, true)
      }
    }
  }

  const handleClickDelete = (menu: PoTypeEnum, item: SubMenuItem) => {
    setDeleteTarget({
      menu,
      item,
    })
    setOpenDeleteModal(true)
  }

  const handleStartNewChat = () => {
    router.push({
      pathname: smartiesRoutes.basicSearch.home,
    })
  }

  useImperativeHandle(ref, () => {
    return {
      updateSecondMenu: (menu: PoTypeEnum) => {
        updateSubmenuList(menu, true)
      },
    }
  }, [])

  useEffect(() => {
    EventBus$.on('update-session', (data: PoTypeEnum) => {
      updateSubmenuList(data, true)
    })
  }, [])

  return (
    <>
      <div
        className={clsx(
          'hide-scrollbar h-screen-minus-header overflow-y-auto px-4 pb-4',
          className,
        )}
        data-component='Menu'>
        <div className={clsx('mb-4 mt-4 border-b border-dashed pb-4')}>
          <div
            className='flex-center h-9 cursor-pointer rounded-sm border-2 text-foreground hover:bg-secondary-hover'
            onClick={handleStartNewChat}>
            <PlusIcon className='mr-1 h-4 w-4' />
            <Text type={TextEnum.Body_medium}>{t('button.newSearch')}</Text>
          </div>
        </div>

        {Object.values(PoTypeEnum).map((menu) => (
          <div key={menu}>
            <div
              className='flex h-8 cursor-pointer items-center justify-between p-2'
              onClick={() => toggleMenu(menu)}>
              <FolderIcon className='mr-3 h-4 w-4 text-secondary-black-3' />
              <Text type={TextEnum.Body_medium} className='flex-1 text-secondary-black-3'>
                {t(`title.${menu.toLocaleLowerCase()}`)}
              </Text>
              {expandedMenus.includes(menu) ? (
                <ChevronUp size={16} className='text-secondary-black-3' />
              ) : (
                <ChevronDown size={16} className='text-secondary-black-3' />
              )}
            </div>

            {expandedMenus.includes(menu) && (
              <>
                {secondMenuItems[menu]?.loading && <SubMenuSkeleton />}
                {!secondMenuItems[menu]?.loading && secondMenuItems[menu]?.items.length === 0 && (
                  <>
                    <div className='flex-center my-10 flex-col'>
                      <Image src='/images/no_data.svg' alt='no data' width={36} height={36} />
                      <Text type={TextEnum.Body_medium} className='mt-3 text-gray'>
                        {t('display.noData')}
                      </Text>
                    </div>
                  </>
                )}
                {secondMenuItems[menu]?.items.map((item, index) => {
                  return (
                    <MenuItemTitle
                      key={`${JSON.stringify(item)}_${index}`}
                      menu={menu}
                      item={item}
                      activeItem={activeItem}
                      onClickChat={() => {
                        handleClickChat(menu, item)
                      }}
                      onClickDelete={() => {
                        handleClickDelete(menu, item)
                      }}
                      onConfirmRename={(_, __, title) => {
                        if (!title || title === item.title) return
                        updateSession({
                          sessionId: item.sessionId,
                          title,
                        }).then((res) => {
                          if (res.result === 'ok') {
                            updateSubmenuList(menu, true)
                          }
                        })
                      }}
                    />
                  )
                })}
                {secondMenuItems[menu]?.pagination.lastPageKey && (
                  <Text
                    type={TextEnum.Body_medium}
                    className='my-2 w-full cursor-pointer text-center text-base text-primary hover:text-primary-dark'
                    onClick={() => updateSubmenuList(menu, false)}>
                    {t('button.loadMore')}
                  </Text>
                )}
              </>
            )}
          </div>
        ))}
      </div>
      <DeleteMenuModal
        open={openDeleteModal}
        onClose={() => {
          setOpenDeleteModal(false)
        }}
        onConfirm={handleDeleteChat}
      />
    </>
  )
})

Menu.displayName = 'Menu'
export default Menu
